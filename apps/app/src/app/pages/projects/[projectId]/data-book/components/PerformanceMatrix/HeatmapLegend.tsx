import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { ScoringInfo } from './ScoringInfo';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between px-4 min-w-max items-center">
        <Popover>
          <Popover.Trigger>
            <Tooltip.Root delayDuration={100}>
              <Tooltip.Trigger>
                <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                  {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
                </Button>
              </Tooltip.Trigger>
              <Tooltip.Content align="start">
                {messages('performanceDetails.issueReportsTable.scoringInfo.tooltipText')}
              </Tooltip.Content>
            </Tooltip.Root>
          </Popover.Trigger>
          <Popover.Content align="start" className="p-0 max-h-[340px]">
            <ScoringInfo />
          </Popover.Content>
        </Popover>
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <div key={level} className="p-2 last:pr-0">
                <Tooltip.Root delayDuration={500}>
                  <Tooltip.Trigger asChild>
                    <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
                  </Tooltip.Trigger>
                  <Tooltip.Content side="top" sideOffset={0} color="dark" className="px-3 py-2 max-md:w-auto">
                    <ScoringInfo />
                  </Tooltip.Content>
                </Tooltip.Root>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
